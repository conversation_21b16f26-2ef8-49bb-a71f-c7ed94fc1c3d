<?php

namespace App\Jobs;

use App\Services\SNSNotificationService;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ProcessEndpointAndNotifyJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $timeout = 600; // 10 minutes per chunk
    public $tries = 3;
    public $backoff = [60, 120, 300]; // Exponential backoff

    protected $messageId;
    protected $notificationTitle;
    protected $notificationBody;
    protected $offset;
    protected $chunkSize;

    public function __construct($messageId, $notificationTitle, $notificationBody, $offset, $chunkSize)
    {
        $this->messageId = $messageId;
        $this->notificationTitle = $notificationTitle;
        $this->notificationBody = $notificationBody;
        $this->offset = $offset;
        $this->chunkSize = $chunkSize;
    }

    public function handle(SNSNotificationService $snsNotificationService)
    {
        try {
            Log::channel('stack_sns')->info("🔄 Processing chunk: offset {$this->offset}, size {$this->chunkSize} for message {$this->messageId}");

            $startTime = microtime(true);
            $processedCount = 0;
            $endpointUpdated = 0;
            $endpointCreated = 0;
            $notificationsSent = 0;
            $errors = 0;

            // Get users in this chunk
            $users = DB::table('user_access')
                ->whereNotNull('fcm_token')
                ->select('user_id', 'fcm_token', 'sns_endpoint_arn')
                ->offset($this->offset)
                ->limit($this->chunkSize)
                ->get();

            $validEndpoints = [];

            foreach ($users as $user) {
                try {
                    $processedCount++;
                    $endpointArn = null;

                    // Check if user has SNS endpoint
                    if (empty($user->sns_endpoint_arn)) {
                        // Create new endpoint
                        $endpointArn = $snsNotificationService->getOrCreateSnsEndpoint($user->user_id, $user->fcm_token);
                        if ($endpointArn) {
                            $endpointCreated++;
                            Log::channel('stack_sns')->debug("✅ Created SNS endpoint for user {$user->user_id}");
                        }
                    } else {
                        // Check if existing endpoint is enabled, re-enable if disabled
                        $endpointArn = $snsNotificationService->validateAndReenableEndpoint($user->user_id, $user->sns_endpoint_arn, $user->fcm_token);
                        if ($endpointArn) {
                            if ($endpointArn !== $user->sns_endpoint_arn) {
                                $endpointUpdated++;
                                Log::channel('stack_sns')->debug("🔄 Re-enabled SNS endpoint for user {$user->user_id}");
                            }
                        }
                    }

                    // If we have a valid endpoint, add to notification list
                    if ($endpointArn) {
                        $validEndpoints[] = (object)[
                            'user_id' => $user->user_id,
                            'sns_endpoint_arn' => $endpointArn
                        ];
                    }

                    // Small delay to prevent API rate limiting
                    if ($processedCount % 20 === 0) {
                        usleep(100000); // 0.1 second pause every 20 requests
                    }

                } catch (\Exception $e) {
                    $errors++;
                    Log::channel('stack_sns')->warning("⚠️ Failed to process user {$user->user_id}: " . $e->getMessage());
                }
            }

            // Send notifications to users with valid endpoints
            if (!empty($validEndpoints)) {
                $notificationChunkSize = 25;
                $notificationChunks = array_chunk($validEndpoints, $notificationChunkSize);

                foreach ($notificationChunks as $notificationChunk) {
                    SendBulkPushNotificationJob::dispatch(
                        $notificationChunk,
                        $this->notificationTitle,
                        $this->notificationBody,
                        $this->messageId,
                        null
                    )->onQueue('SendMessageNotificationUser');

                    $notificationsSent += count($notificationChunk);
                }
            }

            $endTime = microtime(true);
            $executionTime = round($endTime - $startTime, 2);

            Log::channel('stack_sns')->info("✅ Chunk completed for message {$this->messageId}", [
                'offset' => $this->offset,
                'processed' => $processedCount,
                'endpoints_created' => $endpointCreated,
                'endpoints_updated' => $endpointUpdated,
                'notifications_queued' => $notificationsSent,
                'errors' => $errors,
                'execution_time' => $executionTime
            ]);

        } catch (\Exception $e) {
            Log::channel('stack_sns')->error("💥 Chunk job failed for message {$this->messageId}, offset {$this->offset}: " . $e->getMessage());
            throw $e;
        }
    }

    public function failed(\Throwable $exception)
    {
        Log::channel('stack_sns')->error("💥 Job permanently failed for message {$this->messageId}, offset {$this->offset}", [
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString()
        ]);
    }
}